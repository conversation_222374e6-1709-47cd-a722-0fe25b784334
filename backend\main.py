from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import duckdb
import pandas as pd
from datetime import datetime
import os

app = FastAPI(title="财务台账查询系统", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据库连接
DB_PATH = "financial_data.duckdb"

# 请求模型
class QueryRequest(BaseModel):
    filters: Dict[str, Any]
    timestamp: str

# 响应模型
class QueryResponse(BaseModel):
    code: int
    message: str
    data: List[List[Any]]
    timestamp: str

# 初始化数据库和表结构
def init_database():
    """初始化数据库和表结构"""
    conn = duckdb.connect(DB_PATH)
    
    # 创建一体化合同台账表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS integrated_contract (
            search_key VARCHAR,
            organization_name VARCHAR,
            project_name VARCHAR,
            project_code VARCHAR,
            contract_name VARCHAR,
            contract_code VARCHAR,
            original_contract_code VARCHAR,
            contract_business_content VARCHAR,
            customer_name VARCHAR,
            customer_code VARCHAR,
            contract_type VARCHAR,
            contract_amount DOUBLE,
            tax_rate VARCHAR,
            settlement_amount DOUBLE,
            prepaid_amount DOUBLE,
            paid_amount DOUBLE,
            invoice_amount DOUBLE,
            payment_ratio DOUBLE,
            payable_balance DOUBLE,
            overdue_amount DOUBLE
        )
    """)
    
    # 创建专项储备表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS special_reserve (
            voucher_number VARCHAR,
            fiscal_year VARCHAR,
            profit_center VARCHAR,
            profit_center_desc VARCHAR,
            text VARCHAR,
            posting_date DATE,
            input_date DATE,
            safety_production_fee DOUBLE,
            type VARCHAR
        )
    """)
    
    # 创建主数据表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS master_data (
            project_code VARCHAR,
            profit_center VARCHAR,
            accounting_organization VARCHAR,
            profit_center_desc VARCHAR,
            profit_center_group_desc VARCHAR
        )
    """)
    
    # 创建付款台账表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS payment_ledger (
            fiscal_year VARCHAR,
            posting_date DATE,
            input_date DATE,
            supplier_type VARCHAR,
            voucher_number VARCHAR,
            profit_center VARCHAR,
            profit_center_desc VARCHAR,
            supplier VARCHAR,
            supplier_desc VARCHAR,
            contract VARCHAR,
            contract_desc VARCHAR,
            text VARCHAR,
            platform_document_number VARCHAR,
            total_payment_amount DOUBLE,
            performance_bond_deduction DOUBLE,
            supply_chain_factoring DOUBLE,
            cost_offset DOUBLE,
            this_profit_center DOUBLE,
            internal_bank_or_deposit DOUBLE,
            internal_bank_customer VARCHAR
        )
    """)
    
    # 创建保证金台账表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS guarantee_ledger (
            serial_number DOUBLE,
            organization VARCHAR,
            organization_name VARCHAR,
            project_code VARCHAR,
            project_name VARCHAR,
            currency VARCHAR,
            deadline TIMESTAMP,
            guarantee_type VARCHAR,
            receiving_party VARCHAR,
            receiving_party_group VARCHAR,
            actual_paid_amount DOUBLE,
            actual_recovered_amount DOUBLE,
            remaining_amount DOUBLE,
            responsible_person VARCHAR
        )
    """)
    
    conn.close()

# 构建查询条件
def build_where_clause(filters: Dict[str, Any], table_name: str) -> str:
    """构建WHERE子句"""
    conditions = []
    
    for key, value in filters.items():
        if not value:
            continue
            
        # 处理金额范围查询
        if key.endswith('Min') and value:
            field_name = key[:-3]  # 移除'Min'后缀
            conditions.append(f"{field_name} >= {float(value)}")
        elif key.endswith('Max') and value:
            field_name = key[:-3]  # 移除'Max'后缀
            conditions.append(f"{field_name} <= {float(value)}")
        # 处理日期范围查询
        elif isinstance(value, list) and len(value) == 2:
            if value[0] and value[1]:
                conditions.append(f"{key} BETWEEN '{value[0]}' AND '{value[1]}'")
        # 处理文本查询
        elif isinstance(value, str) and value.strip():
            conditions.append(f"{key} LIKE '%{value.strip()}%'")
        # 处理数字查询
        elif isinstance(value, (int, float)):
            conditions.append(f"{key} = {value}")
    
    return " AND ".join(conditions) if conditions else "1=1"

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化数据库"""
    init_database()

@app.get("/")
async def root():
    return {"message": "财务台账查询系统API"}

@app.post("/api/query/integrated-contract", response_model=QueryResponse)
async def query_integrated_contract(request: QueryRequest):
    """查询一体化合同台账"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        where_clause = build_where_clause(request.filters, "integrated_contract")
        query = f"""
            SELECT * FROM integrated_contract 
            WHERE {where_clause}
            ORDER BY contract_code DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        # 构造返回数据，第一行为列名
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/special-reserve", response_model=QueryResponse)
async def query_special_reserve(request: QueryRequest):
    """查询专项储备"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        where_clause = build_where_clause(request.filters, "special_reserve")
        query = f"""
            SELECT * FROM special_reserve 
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/master-data", response_model=QueryResponse)
async def query_master_data(request: QueryRequest):
    """查询主数据"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "master_data")
        query = f"""
            SELECT * FROM master_data
            WHERE {where_clause}
            ORDER BY project_code
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/payment-ledger", response_model=QueryResponse)
async def query_payment_ledger(request: QueryRequest):
    """查询付款台账"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "payment_ledger")
        query = f"""
            SELECT * FROM payment_ledger
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/guarantee-ledger", response_model=QueryResponse)
async def query_guarantee_ledger(request: QueryRequest):
    """查询保证金台账"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "guarantee_ledger")
        query = f"""
            SELECT * FROM guarantee_ledger
            WHERE {where_clause}
            ORDER BY deadline DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 导入额外的API端点
from additional_endpoints import (
    query_internal_bank,
    query_internal_reconciliation,
    query_subcontractor_settlement,
    query_external_confirmation,
    query_payable_by_supplier,
    query_cost_ledger,
    query_receipt_ledger,
    query_fund_management
)

# 导入资金流动API
from capital_flow_api import router as capital_flow_router

# 注册额外的API端点
@app.post("/api/query/internal-bank", response_model=QueryResponse)
async def api_query_internal_bank(request: QueryRequest):
    return await query_internal_bank(request)

@app.post("/api/query/internal-reconciliation", response_model=QueryResponse)
async def api_query_internal_reconciliation(request: QueryRequest):
    return await query_internal_reconciliation(request)

@app.post("/api/query/subcontractor-settlement", response_model=QueryResponse)
async def api_query_subcontractor_settlement(request: QueryRequest):
    return await query_subcontractor_settlement(request)

@app.post("/api/query/external-confirmation", response_model=QueryResponse)
async def api_query_external_confirmation(request: QueryRequest):
    return await query_external_confirmation(request)

@app.post("/api/query/payable-by-supplier", response_model=QueryResponse)
async def api_query_payable_by_supplier(request: QueryRequest):
    return await query_payable_by_supplier(request)

@app.post("/api/query/cost-ledger", response_model=QueryResponse)
async def api_query_cost_ledger(request: QueryRequest):
    return await query_cost_ledger(request)

@app.post("/api/query/receipt-ledger", response_model=QueryResponse)
async def api_query_receipt_ledger(request: QueryRequest):
    return await query_receipt_ledger(request)

@app.post("/api/query/fund-management", response_model=QueryResponse)
async def api_query_fund_management(request: QueryRequest):
    return await query_fund_management(request)

# 注册资金流动API路由
app.include_router(capital_flow_router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
