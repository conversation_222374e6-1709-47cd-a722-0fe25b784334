<template>
  <div class="sim-table-component">
    <!-- 筛选面板 -->
    <div v-if="showFilter" class="filter-panel">
      <div class="filter-row">
        <el-select
          v-model="filterColumn"
          placeholder="选择列"
          size="small"
          style="width: 120px"
          clearable
        >
          <el-option
            v-for="(header, index) in headers"
            :key="index"
            :label="header"
            :value="index.toString()"
          />
        </el-select>
        
        <el-input
          v-model="filterText"
          placeholder="输入筛选内容"
          size="small"
          style="width: 200px"
          clearable
          @keyup.enter="applyFilter"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-button type="primary" size="small" @click="applyFilter">
          <el-icon><Search /></el-icon> 筛选
        </el-button>
        
        <el-button size="small" @click="clearFilter">
          <el-icon><Refresh /></el-icon> 清除
        </el-button>
      </div>
    </div>

    <!-- 表格容器 -->
    <div
      class="table-wrapper"
      :style="{
        height: height ? `${height}px` : 'auto',
        width: width ? `${width}px` : '100%'
      }"
    >
      <!-- 表格头部 -->
      <div class="table-header-container">
        <table class="sim-table header-table">
          <thead>
            <tr>
              <th
                v-for="(header, index) in headers"
                :key="index"
                class="table-header-cell"
                @click="sortByColumn(index)"
              >
                <div class="header-content">
                  <span class="header-text">{{ header }}</span>
                  <el-icon v-if="sortColumn === index.toString()" class="sort-icon">
                    <ArrowUp v-if="sortDirection === 'asc'" />
                    <ArrowDown v-else />
                  </el-icon>
                </div>
              </th>
            </tr>
          </thead>
        </table>
      </div>

      <!-- 表格主体 -->
      <div class="table-body-container" :style="{ maxHeight: height ? `${height - 60}px` : '400px' }">
        <table class="sim-table body-table">
          <tbody>
            <tr
              v-for="(row, rowIndex) in displayData"
              :key="rowIndex"
              class="table-row"
              :class="{ 'row-even': rowIndex % 2 === 0, 'row-odd': rowIndex % 2 === 1 }"
            >
              <td
                v-for="(cell, cellIndex) in row"
                :key="cellIndex"
                class="table-cell"
                :class="{ 'numeric-cell': isNumericValue(cell) }"
                :title="String(cell)"
              >
                {{ formatCellValue(cell) }}
              </td>
            </tr>
            
            <!-- 空数据提示 -->
            <tr v-if="displayData.length === 0" class="empty-row">
              <td :colspan="headers.length" class="empty-cell">
                <el-empty description="暂无数据" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination && totalRows > pageSize" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="totalRows"
        layout="total, prev, pager, next, jumper"
        @current-change="handlePageChange"
        size="small"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Search, Refresh, ArrowUp, ArrowDown } from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  // 表格数据 (二维数组，第一行为表头)
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  // 表格宽度
  width: {
    type: Number,
    default: null
  },
  // 表格高度
  height: {
    type: Number,
    default: 400
  },
  // 是否显示筛选面板
  showFilter: {
    type: Boolean,
    default: true
  },
  // 是否显示分页
  showPagination: {
    type: Boolean,
    default: false
  },
  // 每页显示条数
  pageSize: {
    type: Number,
    default: 20
  }
})

// 响应式状态
const filterColumn = ref('')
const filterText = ref('')
const sortColumn = ref('')
const sortDirection = ref('asc')
const currentPage = ref(1)

// 计算属性
const headers = computed(() => {
  return props.data.length > 0 ? props.data[0] : []
})

const dataRows = computed(() => {
  return props.data.length > 1 ? props.data.slice(1) : []
})

// 筛选后的数据
const filteredData = computed(() => {
  let result = [...dataRows.value]
  
  // 应用筛选
  if (filterColumn.value && filterText.value) {
    const columnIndex = parseInt(filterColumn.value)
    const searchText = filterText.value.toLowerCase()
    
    result = result.filter(row => {
      const cellValue = String(row[columnIndex] || '').toLowerCase()
      return cellValue.includes(searchText)
    })
  }
  
  // 应用排序
  if (sortColumn.value) {
    const columnIndex = parseInt(sortColumn.value)
    result.sort((a, b) => {
      const aVal = a[columnIndex] || ''
      const bVal = b[columnIndex] || ''
      
      // 数值比较
      if (isNumericValue(aVal) && isNumericValue(bVal)) {
        const numA = parseFloat(String(aVal).replace(/[^\d.-]/g, ''))
        const numB = parseFloat(String(bVal).replace(/[^\d.-]/g, ''))
        return sortDirection.value === 'asc' ? numA - numB : numB - numA
      }
      
      // 字符串比较
      const strA = String(aVal).toLowerCase()
      const strB = String(bVal).toLowerCase()
      if (sortDirection.value === 'asc') {
        return strA.localeCompare(strB)
      } else {
        return strB.localeCompare(strA)
      }
    })
  }
  
  return result
})

// 分页后的显示数据
const displayData = computed(() => {
  if (!props.showPagination) {
    return filteredData.value
  }
  
  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return filteredData.value.slice(start, end)
})

const totalRows = computed(() => filteredData.value.length)

// 方法
function applyFilter() {
  currentPage.value = 1
}

function clearFilter() {
  filterColumn.value = ''
  filterText.value = ''
  sortColumn.value = ''
  sortDirection.value = 'asc'
  currentPage.value = 1
}

function sortByColumn(columnIndex) {
  const columnStr = columnIndex.toString()
  if (sortColumn.value === columnStr) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortColumn.value = columnStr
    sortDirection.value = 'asc'
  }
  currentPage.value = 1
}

function handlePageChange(page) {
  currentPage.value = page
}

function isNumericValue(value) {
  if (value === null || value === undefined || value === '') return false
  const str = String(value).replace(/[,¥$%\s]/g, '')
  return !isNaN(str) && !isNaN(parseFloat(str))
}

function formatCellValue(value) {
  if (value === null || value === undefined) return ''
  
  // 数值格式化
  if (isNumericValue(value)) {
    const numStr = String(value).replace(/[,¥$%\s]/g, '')
    const num = parseFloat(numStr)
    if (!isNaN(num)) {
      // 如果原值包含货币符号，保留格式
      if (String(value).includes('¥')) {
        return '¥' + num.toLocaleString()
      }
      if (String(value).includes('%')) {
        return num.toLocaleString() + '%'
      }
      return num.toLocaleString()
    }
  }
  
  return String(value)
}

// 暴露方法给父组件
defineExpose({
  clearFilter,
  applyFilter
})
</script>

<style scoped>
.sim-table-component {
  width: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 筛选面板样式 */
.filter-panel {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e0e6ed;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 表格容器样式 */
.table-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 0 0 12px 12px;
}

.table-header-container {
  background: #ffffff;
  border-bottom: 2px solid #e0e6ed;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-body-container {
  overflow-y: auto;
  overflow-x: auto;
  background: #ffffff;
}

/* 表格基础样式 */
.sim-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
  line-height: 1.5;
}

/* 表头样式 */
.table-header-cell {
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  text-align: center;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.table-header-cell:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
}

.table-header-cell:last-child {
  border-right: none;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.header-text {
  font-size: 13px;
  font-weight: 600;
}

.sort-icon {
  font-size: 12px;
  opacity: 0.8;
}

/* 表格行样式 */
.table-row {
  transition: all 0.2s ease;
  cursor: pointer;
}

.table-row:hover {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.row-even {
  background: #ffffff;
}

.row-odd {
  background: #fafbfc;
}

.table-row:hover.row-even,
.table-row:hover.row-odd {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
}

/* 表格单元格样式 */
.table-cell {
  padding: 10px 16px;
  border-right: 1px solid #e8eaed;
  border-bottom: 1px solid #e8eaed;
  text-align: left;
  vertical-align: middle;
  transition: all 0.2s ease;
  position: relative;
}

.table-cell:last-child {
  border-right: none;
}

.numeric-cell {
  text-align: right;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
  color: #2563eb;
}

/* 空数据样式 */
.empty-row {
  background: #ffffff;
}

.empty-cell {
  padding: 40px 20px;
  text-align: center;
  border: none;
  color: #909399;
}

/* 分页样式 */
.pagination-container {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e0e6ed;
  display: flex;
  justify-content: center;
}

/* 滚动条样式 */
.table-body-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-body-container::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 4px;
}

.table-body-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.table-body-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-row > * {
    width: 100%;
    margin-bottom: 8px;
  }

  .table-header-cell,
  .table-cell {
    padding: 8px 12px;
    font-size: 12px;
  }

  .header-text {
    font-size: 12px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.table-row {
  animation: fadeIn 0.3s ease-out;
}

/* 特殊数据类型样式 */
.table-cell:contains("¥") {
  color: #16a085 !important;
  font-weight: 500;
}

.table-cell:contains("%") {
  color: #e67e22 !important;
  font-weight: 500;
}

/* 数据高亮 */
.table-cell {
  position: relative;
}

.table-cell:hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(102, 126, 234, 0.05);
  pointer-events: none;
}
</style>
