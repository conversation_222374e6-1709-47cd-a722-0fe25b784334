import { createRouter, createWebHistory } from 'vue-router'
import BudgetReportView from '../views/BudgetReportView.vue'
import SalaryTaxView from '../views/SalaryTaxView.vue'
import QuickQueryView from '../views/QuickQueryView.vue'
import OtherFeaturesView from '../views/OtherFeaturesView.vue'
import DataAnalysisView from '../views/DataAnalysisView.vue'
import CapitalFlowView from '../views/CapitalFlowView.vue'
import OneClickReportView from '../views/OneClickReportView.vue'
import SamrtWorkerView from '../views/SamrtWorkerView.vue'
import SmartReconciliationView from '../views/SmartReconciliationView.vue'
import SettingsView from '../views/SettingsView.vue'
import ProjectReportView from '../views/ProjectReportView.vue'
import CapitalFlowDemo from '../views/CapitalFlowDemo.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/data-analysis'
    },
    {
      path: '/data-analysis',
      name: 'data-analysis',
      component: DataAnalysisView,
      meta: {
        title: '我的账面',
        icon: 'DataAnalysis'
      }
    },
    {
      path: '/budget-report',
      name: 'budget-report',
      component: BudgetReportView,
      meta: {
        title: '报表快算',
        icon: 'TrendCharts'
      }
    },
    {
      path: '/salary-tax',
      name: 'salary-tax',
      component: SalaryTaxView,
      meta: {
        title: '薪酬个税',
        icon: 'Money'
      }
    },
    {
      path: '/capital-flow',
      name: 'capital-flow',
      component: CapitalFlowView,
      meta: {
        title: '资金流动',
        icon: 'SwitchButton'
      }
    },
    {
      path: '/one-click-report',
      name: 'one-click-report',
      component: OneClickReportView,
      meta: {
        title: '一键报告',
        icon: 'Document'
      }
    },
    {
      path: '/project-report',
      name: 'project-report',
      component: ProjectReportView,
      meta: {
        title: '项目台账',
        icon: 'Folder'
      }
    },
    {
      path: '/samrt-worker',
      name: 'samrt-worker',
      component: SamrtWorkerView,
      meta: {
        title: '工人专区',
        icon: 'User'
      }
    },
    {
      path: '/smart-reconciliation',
      name: 'smart-reconciliation',
      component: SmartReconciliationView,
      meta: {
        title: '对账抵消',
        icon: 'Files'
      }
    },
    {
      path: '/quick-query',
      name: 'quick-query',
      component: QuickQueryView,
      meta: {
        title: '快速查询',
        icon: 'Search'
      }
    },
    {
      path: '/settings',
      name: 'settings',
      component: SettingsView,
      meta: {
        title: '系统设置',
        icon: 'Setting'
      }
    },
    //{
    //  path: '/capital-flow-demo',
    //  name: 'capital-flow-demo',
    //  component: CapitalFlowDemo,
    //  meta: {
    //    title: '资金流动图表演示',
    //    icon: 'TrendCharts'
    //  }
    //},
    // {
    //   path: '/table-demo',
    //   name: 'table-demo',
    //   component: TableComponentDemo,
    //   meta: {
    //     title: '表格组件演示',
    //     icon: 'fa-table'
    //   }
    // },
    // {
    //   path: '/vtable-test',
    //   name: 'vtable-test',
    //   component: VTableTestView,
    //   meta: {
    //     title: 'VTable测试',
    //     icon: 'fa-table'
    //   }
    // },
    // {
    //   path: '/vtable-scroll-test',
    //   name: 'vtable-scroll-test',
    //   component: VTableScrollTest,
    //   meta: {
    //     title: 'VTable滚动测试',
    //     icon: 'fa-arrows-h'
    //   }
    // }
  ],
})

export default router
